
const approvalTypeOptions = [
  { label: '调解办结', value: '1' },
  { label: '延期审批', value: '2' },
  { label: '案管办结', value: '3' }
]

enum approvalTypeEnum {
	MEDIATE_CLOSE = 1,
	DELAY = 2,
	CASE_MANAGE_CLOSE = 3
}

const approvalStatusOptions = [
  { label: '待审核', value: '1' },
  { label: '审核中', value: '5' },
  { label: '审核完成', value: '10' }
]

const approvalResultOptions = [
  { label: '通过', value: '1' },
  { label: '失败', value: '5' }
]

export default {
  approvalStatusOptions,
  approvalResultOptions,
  approvalTypeOptions,
	approvalTypeEnum
}
