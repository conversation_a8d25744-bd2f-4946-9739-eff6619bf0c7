

enum approvalTypeEnum {
	MEDIATE_CLOSE = 1,
	DELAY = 2,
	CASE_MANAGE_CLOSE = 3
}

const approvalTypeOptions = [
  { label: '调解办结', value: approvalTypeEnum.MEDIATE_CLOSE},
  { label: '延期审批', value: approvalTypeEnum.DELAY },
  { label: '案管办结', value: approvalTypeEnum.CASE_MANAGE_CLOSE }
]

// 具体枚举类：继承 Enums 并定义静态 Enum 实例
export class approvalTypeEnumT extends Enums {
	static readonly MEDIATE_CLOSE = new Enum(1, "调解办结");
	static readonly DELAY = new Enum(2, "审核中");
	static readonly CASE_MANAGE_CLOSE = new Enum(3, "审核完成");
}



const approvalStatusOptions = [
	{ label: '待审核', value: '1' },
	{ label: '审核中', value: '5' },
	{ label: '审核完成', value: '10' }
]

const approvalResultOptions = [
  { label: '通过', value: '1' },
  { label: '失败', value: '5' }
]

export default {
  approvalStatusOptions,
  approvalResultOptions,
  approvalTypeOptions,
	approvalTypeEnum
}
